@import url('https://fonts.googleapis.com/css2?family=Helvetica+Neue:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --nike-black: #111111;
  --nike-white: #ffffff;
  --nike-gray-light: #f5f5f5;
  --nike-gray-medium: #757575;
  --nike-gray-dark: #111111;
  --nike-orange: #ff6900;
  --nike-red: #e74c3c;
}

body {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  color: var(--nike-black);
  background-color: var(--nike-white);
  margin: 0;
  padding: 0;
}

a {
  text-decoration: none;
  color: inherit;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--nike-gray-medium);
}

button {
  font-family: inherit;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.container {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 20px;
}

.btn-primary {
  background-color: var(--nike-black);
  color: var(--nike-white);
  padding: 12px 24px;
  border-radius: 30px;
  font-weight: 500;
  font-size: 16px;
  border: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-primary:hover {
  background-color: var(--nike-gray-medium);
}

.btn-secondary {
  background-color: var(--nike-white);
  color: var(--nike-black);
  padding: 12px 24px;
  border: 1px solid var(--nike-black);
  border-radius: 30px;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-secondary:hover {
  background-color: var(--nike-gray-light);
  color: var(--nike-black);
}

.section-padding {
  padding: 60px 0;
}

@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .section-padding {
    padding: 40px 0;
  }
}
