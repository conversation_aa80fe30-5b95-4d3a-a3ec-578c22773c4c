import { useState, useEffect } from 'react'
import './Hero.css'

const Hero = () => {
  const [currentSlide, setCurrentSlide] = useState(0)

  const slides = [
    {
      id: 1,
      image: "https://images.unsplash.com/photo-1542291026-7eec264c27ff?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
    },
    {
      id: 2,
      image: "https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1887&q=80"
    },
    {
      id: 3,
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
    },
    {
      id: 4,
      image: "https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
    }
  ]

  // Auto-advance slides
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 5000) // Change slide every 5 seconds

    return () => clearInterval(timer)
  }, [slides.length])

  return (
    <section className="hero">
      <div className="hero-content">
        {/* Hero Carousel */}
        <div className="hero-carousel">
          <div className="carousel-container">
            {slides.map((slide, index) => (
              <div
                key={slide.id}
                className={`carousel-slide ${index === currentSlide ? 'active' : ''}`}
              >
                <img
                  src={slide.image}
                  alt="Nike Hero"
                  className="hero-img"
                />
              </div>
            ))}
          </div>

          {/* Static overlay with consistent text */}
          <div className="hero-overlay">
            <div className="hero-text">
              <h1 className="hero-title">JUST DO IT</h1>
              <p className="hero-subtitle">
                Move, shop, customize and celebrate with us.
              </p>
              <p className="hero-description">
                No matter what you feel like doing today, It's better as a Member.
              </p>
              <div className="hero-buttons">
                <button className="btn-secondary">Shop Now</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Secondary hero sections */}
      <div className="hero-secondary">
        <div className="container">
          <div className="hero-grid">
            <div className="hero-card">
              <img 
                src="https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1887&q=80" 
                alt="Running Shoes"
                className="hero-card-img"
              />
              <div className="hero-card-content">
                <h3>Run Your Way</h3>
                <p>Find your perfect running companion</p>
                <button className="btn-secondary">Shop Running</button>
              </div>
            </div>

            <div className="hero-card">
              <img 
                src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" 
                alt="Basketball"
                className="hero-card-img"
              />
              <div className="hero-card-content">
                <h3>Court Ready</h3>
                <p>Dominate the game with Jordan</p>
                <button className="btn-secondary">Shop Basketball</button>
              </div>
            </div>

            <div className="hero-card">
              <img 
                src="https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" 
                alt="Lifestyle"
                className="hero-card-img"
              />
              <div className="hero-card-content">
                <h3>Street Style</h3>
                <p>Express yourself with Nike lifestyle</p>
                <button className="btn-secondary">Shop Lifestyle</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
