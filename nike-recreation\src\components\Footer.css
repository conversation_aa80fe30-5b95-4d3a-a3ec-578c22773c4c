.footer {
  background-color: var(--nike-black);
  color: var(--nike-white);
  margin-top: auto;
}

.newsletter-section {
  background-color: var(--nike-gray-dark);
  padding: 60px 0;
  text-align: center;
}

.newsletter-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 12px;
}

.newsletter-content p {
  font-size: 1.1rem;
  margin-bottom: 32px;
  opacity: 0.8;
}

.newsletter-form {
  display: flex;
  justify-content: center;
  gap: 16px;
  max-width: 400px;
  margin: 0 auto;
}

.newsletter-input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 30px;
  font-size: 16px;
  outline: none;
}

.newsletter-input::placeholder {
  color: var(--nike-gray-medium);
}

.footer-content {
  padding: 60px 0 40px;
}

.footer-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: 40px;
}

.footer-column h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.footer-column ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.footer-column li {
  margin-bottom: 12px;
}

.footer-column a {
  color: var(--nike-white);
  text-decoration: none;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.footer-column a:hover {
  opacity: 1;
}

.footer-logo {
  margin-bottom: 20px;
}

.footer-description {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 24px;
  opacity: 0.8;
}

.social-links {
  display: flex;
  gap: 16px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: var(--nike-white);
  text-decoration: none;
  transition: background-color 0.2s ease;
}

.social-link:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 24px 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-legal {
  display: flex;
  align-items: center;
  gap: 32px;
}

.legal-links {
  display: flex;
  gap: 24px;
}

.legal-links a {
  color: var(--nike-white);
  text-decoration: none;
  opacity: 0.7;
  font-size: 14px;
  transition: opacity 0.2s ease;
}

.legal-links a:hover {
  opacity: 1;
}

.footer-location p {
  font-size: 14px;
  opacity: 0.7;
  margin: 0;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .newsletter-section {
    padding: 40px 0;
  }

  .newsletter-content h3 {
    font-size: 1.5rem;
  }

  .newsletter-form {
    flex-direction: column;
    gap: 12px;
  }

  .footer-grid {
    grid-template-columns: 1fr;
    gap: 32px;
  }

  .footer-content {
    padding: 40px 0 30px;
  }

  .footer-bottom-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .footer-legal {
    flex-direction: column;
    gap: 16px;
  }

  .legal-links {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .newsletter-section {
    padding: 30px 0;
  }

  .newsletter-content h3 {
    font-size: 1.3rem;
  }

  .newsletter-content p {
    font-size: 1rem;
  }

  .footer-content {
    padding: 30px 0 20px;
  }

  .footer-grid {
    gap: 24px;
  }

  .footer-column h4 {
    font-size: 1rem;
  }

  .social-links {
    justify-content: center;
  }

  .legal-links {
    flex-direction: column;
    gap: 8px;
  }
}
