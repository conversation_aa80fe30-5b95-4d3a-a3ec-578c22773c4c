.header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: var(--nike-white);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.top-banner {
  background-color: var(--nike-gray-light);
  padding: 8px 0;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
}

.main-header {
  padding: 16px 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  position: relative;
  width: 40px;
  height: 40px;
  justify-content: center;
  align-items: center;
}

.hamburger {
  width: 24px;
  height: 18px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.hamburger-line {
  width: 100%;
  height: 2px;
  background-color: var(--nike-black);
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transform-origin: center;
}

/* Hamburger Animation States */
.mobile-menu-btn.open .hamburger-line:nth-child(1) {
  transform: translateY(8px) rotate(45deg);
}

.mobile-menu-btn.open .hamburger-line:nth-child(2) {
  opacity: 0;
  transform: scaleX(0);
}

.mobile-menu-btn.open .hamburger-line:nth-child(3) {
  transform: translateY(-8px) rotate(-45deg);
}

/* Hover effect */
.mobile-menu-btn:hover .hamburger-line {
  background-color: var(--nike-gray-medium);
}

.mobile-menu-btn.open:hover .hamburger-line {
  background-color: var(--nike-black);
}

.logo svg {
  fill: var(--nike-black);
}

.desktop-nav ul {
  display: flex;
  list-style: none;
  gap: 32px;
  margin: 0;
  padding: 0;
}

.desktop-nav a {
  font-weight: 500;
  font-size: 16px;
  color: var(--nike-black);
  text-decoration: none;
  transition: color 0.2s ease;
}

.desktop-nav a:hover {
  color: var(--nike-gray-medium);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-container {
  display: flex;
  align-items: center;
  background-color: var(--nike-gray-light);
  border-radius: 20px;
  padding: 8px 16px;
  gap: 8px;
}

.search-input {
  border: none;
  background: none;
  outline: none;
  font-size: 14px;
  width: 180px;
}

.search-input::placeholder {
  color: var(--nike-gray-medium);
}

.icon-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.icon-btn:hover {
  background-color: var(--nike-gray-light);
}

.mobile-nav {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--nike-white);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.05);

  /* Animation properties */
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.mobile-nav.open {
  max-height: 400px;
  opacity: 1;
  transform: translateY(0);
}

.mobile-nav-content {
  padding: 32px 24px;
}

.mobile-nav ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mobile-nav li {
  margin-bottom: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transform: translateX(-20px);
  opacity: 0;
  animation: slideInLeft 0.3s ease-out forwards;
}

.mobile-nav li:nth-child(1) { animation-delay: 0.1s; }
.mobile-nav li:nth-child(2) { animation-delay: 0.15s; }
.mobile-nav li:nth-child(3) { animation-delay: 0.2s; }
.mobile-nav li:nth-child(4) { animation-delay: 0.25s; }
.mobile-nav li:nth-child(5) { animation-delay: 0.3s; }
.mobile-nav li:nth-child(6) { animation-delay: 0.35s; }

.mobile-nav li:last-child {
  border-bottom: none;
}

.mobile-nav a {
  font-size: 20px;
  font-weight: 500;
  color: var(--nike-black);
  text-decoration: none;
  display: block;
  padding: 20px 0;
  position: relative;
  transition: all 0.3s ease;
}

.mobile-nav a:hover {
  color: var(--nike-gray-medium);
  padding-left: 8px;
}

.mobile-nav a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--nike-black);
  transition: width 0.3s ease;
}

.mobile-nav a:hover::after {
  width: 40px;
}

/* Slide in animation for menu items */
@keyframes slideInLeft {
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .mobile-menu-btn {
    display: flex;
  }

  .desktop-nav {
    display: none;
  }

  .search-container {
    display: none;
  }

  .header-actions {
    gap: 8px;
  }

  .mobile-nav {
    display: block;
  }

  /* Reset animations on mobile for better performance */
  .mobile-nav li {
    animation: none;
    transform: none;
    opacity: 1;
  }

  .mobile-nav.open li {
    animation: slideInLeft 0.3s ease-out forwards;
    transform: translateX(-20px);
    opacity: 0;
  }

  .logo svg {
    width: 40px;
    height: 16px;
  }
}

@media (max-width: 480px) {
  .header-actions .icon-btn:nth-child(1),
  .header-actions .icon-btn:nth-child(2) {
    display: none;
  }
}
