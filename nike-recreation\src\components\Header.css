.header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: var(--nike-white);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.top-banner {
  background-color: var(--nike-gray-light);
  padding: 8px 0;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
}

.main-header {
  padding: 16px 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  position: relative;
  width: 40px;
  height: 40px;
  justify-content: center;
  align-items: center;
}

.hamburger {
  width: 24px;
  height: 18px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.hamburger-line {
  width: 100%;
  height: 2px;
  background-color: var(--nike-black);
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transform-origin: center;
}

/* Hamburger Animation States */
.mobile-menu-btn.open .hamburger-line:nth-child(1) {
  transform: translateY(8px) rotate(45deg);
}

.mobile-menu-btn.open .hamburger-line:nth-child(2) {
  opacity: 0;
  transform: scaleX(0);
}

.mobile-menu-btn.open .hamburger-line:nth-child(3) {
  transform: translateY(-8px) rotate(-45deg);
}

/* Hover effect */
.mobile-menu-btn:hover .hamburger-line {
  background-color: var(--nike-gray-medium);
}

.mobile-menu-btn.open:hover .hamburger-line {
  background-color: var(--nike-black);
}

.logo svg {
  fill: var(--nike-black);
}

.desktop-nav ul {
  display: flex;
  list-style: none;
  gap: 32px;
  margin: 0;
  padding: 0;
}

.desktop-nav a {
  font-weight: 500;
  font-size: 16px;
  color: var(--nike-black);
  text-decoration: none;
  transition: color 0.2s ease;
}

.desktop-nav a:hover {
  color: var(--nike-gray-medium);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-container {
  display: flex;
  align-items: center;
  background-color: var(--nike-gray-light);
  border-radius: 20px;
  padding: 8px 16px;
  gap: 8px;
}

.search-input {
  border: none;
  background: none;
  outline: none;
  font-size: 14px;
  width: 180px;
}

.search-input::placeholder {
  color: var(--nike-gray-medium);
}

.icon-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.icon-btn:hover {
  background-color: var(--nike-gray-light);
}

.mobile-nav {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--nike-white);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.mobile-nav-content {
  padding: 20px;
}

.mobile-nav ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mobile-nav li {
  margin-bottom: 16px;
}

.mobile-nav a {
  font-size: 18px;
  font-weight: 500;
  color: var(--nike-black);
  text-decoration: none;
  display: block;
  padding: 8px 0;
}

.mobile-nav a:hover {
  color: var(--nike-gray-medium);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .mobile-menu-btn {
    display: flex;
  }

  .desktop-nav {
    display: none;
  }

  .search-container {
    display: none;
  }

  .header-actions {
    gap: 8px;
  }

  .mobile-nav {
    display: block;
  }

  .logo svg {
    width: 40px;
    height: 16px;
  }
}

@media (max-width: 480px) {
  .header-actions .icon-btn:nth-child(1),
  .header-actions .icon-btn:nth-child(2) {
    display: none;
  }
}
