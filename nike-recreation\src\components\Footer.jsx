import { Facebook, Twitter, Instagram, Youtube } from 'lucide-react'
import './Footer.css'

const Footer = () => {
  return (
    <footer className="footer">
      <div className="container">
        {/* Newsletter Section */}
        <div className="newsletter-section">
          <div className="newsletter-content">
            <h3>Stay Updated</h3>
            <p>Be the first to know about new products and exclusive offers</p>
            <div className="newsletter-form">
              <input 
                type="email" 
                placeholder="Enter your email"
                className="newsletter-input"
              />
              <button className="btn-primary">Subscribe</button>
            </div>
          </div>
        </div>

        {/* Main Footer Content */}
        <div className="footer-content">
          <div className="footer-grid">
            {/* Company Info */}
            <div className="footer-column">
              <div className="footer-logo">
                <svg width="60" height="22" viewBox="0 0 60 22" fill="none">
                  <path d="M59.14 11.85c-2.13 2.26-5.19 4.24-8.65 5.27-3.46 1.03-7.32 1.03-10.78 0-3.46-1.03-6.52-3.01-8.65-5.27L0 22c4.69-4.69 11.04-7.32 17.71-7.32S30.73 17.31 35.42 22l23.72-10.15z" fill="white"/>
                </svg>
              </div>
              <p className="footer-description">
                Nike delivers innovative products, experiences and services to inspire athletes.
              </p>
              <div className="social-links">
                <a href="#" className="social-link">
                  <Facebook size={20} />
                </a>
                <a href="#" className="social-link">
                  <Twitter size={20} />
                </a>
                <a href="#" className="social-link">
                  <Instagram size={20} />
                </a>
                <a href="#" className="social-link">
                  <Youtube size={20} />
                </a>
              </div>
            </div>

            {/* Products */}
            <div className="footer-column">
              <h4>Products</h4>
              <ul>
                <li><a href="#shoes">Shoes</a></li>
                <li><a href="#clothing">Clothing</a></li>
                <li><a href="#accessories">Accessories</a></li>
                <li><a href="#new">New Releases</a></li>
                <li><a href="#sale">Sale</a></li>
              </ul>
            </div>

            {/* Sports */}
            <div className="footer-column">
              <h4>Sports</h4>
              <ul>
                <li><a href="#running">Running</a></li>
                <li><a href="#basketball">Basketball</a></li>
                <li><a href="#training">Training</a></li>
                <li><a href="#soccer">Soccer</a></li>
                <li><a href="#lifestyle">Lifestyle</a></li>
              </ul>
            </div>

            {/* Support */}
            <div className="footer-column">
              <h4>Support</h4>
              <ul>
                <li><a href="#help">Help Center</a></li>
                <li><a href="#size-guide">Size Guide</a></li>
                <li><a href="#returns">Returns</a></li>
                <li><a href="#shipping">Shipping Info</a></li>
                <li><a href="#contact">Contact Us</a></li>
              </ul>
            </div>

            {/* Company */}
            <div className="footer-column">
              <h4>Company</h4>
              <ul>
                <li><a href="#about">About Nike</a></li>
                <li><a href="#careers">Careers</a></li>
                <li><a href="#news">News</a></li>
                <li><a href="#investors">Investors</a></li>
                <li><a href="#sustainability">Sustainability</a></li>
              </ul>
            </div>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <div className="footer-legal">
              <p>&copy; 2024 Nike Recreation. All rights reserved.</p>
              <div className="legal-links">
                <a href="#privacy">Privacy Policy</a>
                <a href="#terms">Terms of Service</a>
                <a href="#cookies">Cookie Policy</a>
              </div>
            </div>
            <div className="footer-location">
              <p>United States</p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
