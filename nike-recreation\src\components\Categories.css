.categories {
  background-color: var(--nike-gray-light);
}

.section-header {
  text-align: center;
  margin-bottom: 48px;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--nike-black);
}

.sports-header {
  margin-top: 80px;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.sports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.category-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  text-decoration: none;
  color: inherit;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.category-card.large {
  min-height: 400px;
}

.category-card.small {
  min-height: 300px;
}

.category-image {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
}

.category-card:hover .category-image img {
  transform: scale(1.05);
}

.category-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 70%,
    transparent 100%
  );
  color: var(--nike-white);
  padding: 32px;
  text-align: center;
}

.category-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.category-content p {
  font-size: 1.1rem;
  margin-bottom: 20px;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.category-card.small .category-content {
  padding: 24px;
}

.category-card.small .category-content h3 {
  font-size: 1.5rem;
  margin-bottom: 16px;
}

.category-content .btn-primary,
.category-content .btn-secondary {
  background-color: var(--nike-white);
  color: var(--nike-black);
  border: 1px solid var(--nike-white);
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 30px;
  transition: all 0.2s ease;
}

.category-content .btn-primary:hover,
.category-content .btn-secondary:hover {
  background-color: var(--nike-gray-light);
  color: var(--nike-black);
  border-color: var(--nike-gray-light);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .section-header h2 {
    font-size: 2rem;
  }

  .categories-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .sports-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  .category-card.large {
    min-height: 300px;
  }

  .category-card.small {
    min-height: 250px;
  }

  .category-content {
    padding: 24px;
  }

  .category-content h3 {
    font-size: 1.5rem;
  }

  .category-content p {
    font-size: 1rem;
  }

  .category-card.small .category-content {
    padding: 20px;
  }

  .category-card.small .category-content h3 {
    font-size: 1.2rem;
  }

  .sports-header {
    margin-top: 60px;
  }
}

@media (max-width: 480px) {
  .section-header h2 {
    font-size: 1.8rem;
  }

  .categories-grid {
    gap: 12px;
  }

  .sports-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .category-card.large {
    min-height: 250px;
  }

  .category-card.small {
    min-height: 200px;
  }

  .category-content {
    padding: 20px;
  }

  .category-content h3 {
    font-size: 1.3rem;
  }

  .category-card.small .category-content {
    padding: 16px;
  }

  .category-card.small .category-content h3 {
    font-size: 1.1rem;
  }
}
