.hero {
  margin-bottom: 60px;
}

.hero-content {
  position: relative;
  margin-bottom: 40px;
}

.hero-image {
  position: relative;
  height: 70vh;
  min-height: 500px;
  overflow: hidden;
}

.hero-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-text {
  text-align: center;
  color: var(--nike-white);
  max-width: 600px;
  padding: 0 20px;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 16px;
  letter-spacing: 2px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 12px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.hero-description {
  font-size: 1.1rem;
  margin-bottom: 32px;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.hero-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.hero-buttons .btn-secondary {
  background-color: var(--nike-white);
  color: var(--nike-black);
  padding: 16px 32px;
  font-size: 18px;
  font-weight: 600;
  border: 2px solid var(--nike-white);
  border-radius: 30px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.2);
}

.hero-buttons .btn-secondary:hover {
  background-color: var(--nike-gray-light);
  color: var(--nike-black);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

/* Add subtle pulse animation to Shop Now button */
@keyframes pulse {
  0% {
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow: 0 2px 12px rgba(255, 255, 255, 0.4);
  }
  100% {
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.2);
  }
}

.hero-buttons .btn-secondary {
  animation: pulse 2s infinite;
}

.hero-secondary {
  padding: 0 20px;
}

.hero-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.hero-card {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hero-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.hero-card-img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  object-position: center;
}

.hero-card-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 70%,
    transparent 100%
  );
  color: var(--nike-white);
  padding: 24px;
  text-align: center;
}

.hero-card-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.hero-card-content p {
  font-size: 1rem;
  margin-bottom: 16px;
  opacity: 0.9;
}

.hero-card-content .btn-secondary {
  background-color: var(--nike-white);
  color: var(--nike-black);
  border: none;
}

.hero-card-content .btn-secondary:hover {
  background-color: var(--nike-gray-light);
  color: var(--nike-black);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .hero-buttons button {
    width: 220px;
  }

  .hero-buttons .btn-secondary {
    padding: 14px 28px;
    font-size: 16px;
  }

  .hero-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .hero-card-img {
    height: 250px;
  }
}

@media (max-width: 480px) {
  .hero-image {
    height: 50vh;
    min-height: 400px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-card-content {
    padding: 16px;
  }

  .hero-card-content h3 {
    font-size: 1.2rem;
  }
}
