import './Categories.css'

const Categories = () => {
  const categories = [
    {
      id: 1,
      title: "Men",
      subtitle: "Shoes, Clothing & Accessories",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      link: "#men"
    },
    {
      id: 2,
      title: "Women",
      subtitle: "Shoes, Clothing & Accessories",
      image: "https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      link: "#women"
    },
    {
      id: 3,
      title: "Kids",
      subtitle: "Shoes, Clothing & Accessories",
      image: "https://images.unsplash.com/photo-1514590353344-c6d48d4ac7d7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      link: "#kids"
    }
  ]

  const sports = [
    {
      id: 1,
      title: "Running",
      image: "https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1887&q=80",
      link: "#running"
    },
    {
      id: 2,
      title: "Basketball",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      link: "#basketball"
    },
    {
      id: 3,
      title: "Training",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      link: "#training"
    },
    {
      id: 4,
      title: "Soccer",
      image: "https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1993&q=80",
      link: "#soccer"
    }
  ]

  return (
    <section className="categories section-padding">
      <div className="container">
        {/* Main Categories */}
        <div className="section-header">
          <h2>Shop by Category</h2>
        </div>

        <div className="categories-grid">
          {categories.map(category => (
            <a key={category.id} href={category.link} className="category-card large">
              <div className="category-image">
                <img src={category.image} alt={category.title} />
              </div>
              <div className="category-content">
                <h3>{category.title}</h3>
                <p>{category.subtitle}</p>
                <button className="btn-primary">Shop Now</button>
              </div>
            </a>
          ))}
        </div>

        {/* Sports Categories */}
        <div className="section-header sports-header">
          <h2>Shop by Sport</h2>
        </div>

        <div className="sports-grid">
          {sports.map(sport => (
            <a key={sport.id} href={sport.link} className="category-card small">
              <div className="category-image">
                <img src={sport.image} alt={sport.title} />
              </div>
              <div className="category-content">
                <h3>{sport.title}</h3>
                <button className="btn-secondary">Explore</button>
              </div>
            </a>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Categories
