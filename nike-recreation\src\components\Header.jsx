import { useState } from 'react'
import { <PERSON>, ShoppingBag, Menu, X, Heart, User } from 'lucide-react'
import './Header.css'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <header className="header">
      {/* Top banner */}
      <div className="top-banner">
        <div className="container">
          <p>Free Delivery on Orders Over $50</p>
        </div>
      </div>

      {/* Main header */}
      <div className="main-header">
        <div className="container">
          <div className="header-content">
            {/* Mobile menu button */}
            <button className="mobile-menu-btn" onClick={toggleMenu}>
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>

            {/* Logo */}
            <div className="logo">
              <svg width="60" height="22" viewBox="0 0 60 22" fill="none">
                <path d="M59.14 11.85c-2.13 2.26-5.19 4.24-8.65 5.27-3.46 1.03-7.32 1.03-10.78 0-3.46-1.03-6.52-3.01-8.65-5.27L0 22c4.69-4.69 11.04-7.32 17.71-7.32S30.73 17.31 35.42 22l23.72-10.15z" fill="#111"/>
              </svg>
            </div>

            {/* Desktop Navigation */}
            <nav className="desktop-nav">
              <ul>
                <li><a href="#new">New & Featured</a></li>
                <li><a href="#men">Men</a></li>
                <li><a href="#women">Women</a></li>
                <li><a href="#kids">Kids</a></li>
                <li><a href="#sale">Sale</a></li>
                <li><a href="#snkrs">SNKRS</a></li>
              </ul>
            </nav>

            {/* Right side icons */}
            <div className="header-actions">
              <div className="search-container">
                <Search size={20} />
                <input type="text" placeholder="Search" className="search-input" />
              </div>
              <button className="icon-btn">
                <Heart size={20} />
              </button>
              <button className="icon-btn">
                <ShoppingBag size={20} />
              </button>
              <button className="icon-btn">
                <User size={20} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="mobile-nav">
          <div className="mobile-nav-content">
            <ul>
              <li><a href="#new" onClick={toggleMenu}>New & Featured</a></li>
              <li><a href="#men" onClick={toggleMenu}>Men</a></li>
              <li><a href="#women" onClick={toggleMenu}>Women</a></li>
              <li><a href="#kids" onClick={toggleMenu}>Kids</a></li>
              <li><a href="#sale" onClick={toggleMenu}>Sale</a></li>
              <li><a href="#snkrs" onClick={toggleMenu}>SNKRS</a></li>
            </ul>
          </div>
        </div>
      )}
    </header>
  )
}

export default Header
