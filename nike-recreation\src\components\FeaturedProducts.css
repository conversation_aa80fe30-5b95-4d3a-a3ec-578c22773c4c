.featured-products {
  background-color: var(--nike-white);
}

.section-header {
  text-align: center;
  margin-bottom: 48px;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: var(--nike-black);
}

.section-header p {
  font-size: 1.1rem;
  color: var(--nike-gray-medium);
  max-width: 600px;
  margin: 0 auto;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 48px;
}

.product-card {
  background-color: var(--nike-white);
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.product-image-container {
  position: relative;
  background-color: var(--nike-gray-light);
  padding: 20px;
  aspect-ratio: 1;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 8px;
}

.wishlist-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  background-color: var(--nike-white);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.wishlist-btn:hover {
  background-color: var(--nike-black);
  color: var(--nike-white);
}

.new-badge {
  position: absolute;
  top: 16px;
  left: 16px;
  background-color: var(--nike-orange);
  color: var(--nike-white);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.product-info {
  padding: 20px;
}

.product-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--nike-black);
  line-height: 1.3;
}

.product-category {
  font-size: 0.9rem;
  color: var(--nike-gray-medium);
  margin-bottom: 12px;
}

.product-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--nike-black);
}

.product-colors {
  font-size: 0.9rem;
  color: var(--nike-gray-medium);
}

.section-footer {
  text-align: center;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .section-header h2 {
    font-size: 2rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }

  .product-info {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .section-header h2 {
    font-size: 1.8rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
  }

  .product-image-container {
    padding: 16px;
  }

  .product-info {
    padding: 12px;
  }

  .product-name {
    font-size: 1rem;
  }

  .wishlist-btn {
    width: 36px;
    height: 36px;
  }

  .wishlist-btn svg {
    width: 16px;
    height: 16px;
  }
}
