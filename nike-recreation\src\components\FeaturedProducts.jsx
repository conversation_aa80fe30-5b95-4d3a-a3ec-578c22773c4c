import { Heart } from 'lucide-react'
import './FeaturedProducts.css'

const FeaturedProducts = () => {
  const products = [
    {
      id: 1,
      name: "Air Jordan 1 Retro High OG",
      category: "Men's Shoes",
      price: "$170",
      image: "https://images.unsplash.com/photo-1556906781-9a412961c28c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80",
      colors: 3,
      isNew: true
    },
    {
      id: 2,
      name: "Nike Air Max 270",
      category: "Women's Shoes",
      price: "$150",
      image: "https://images.unsplash.com/photo-1549298916-b41d501d3772?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2012&q=80",
      colors: 5,
      isNew: false
    },
    {
      id: 3,
      name: "Nike Dunk Low",
      category: "Unisex Shoes",
      price: "$100",
      image: "https://images.unsplash.com/photo-1600185365483-26d7a4cc7519?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1925&q=80",
      colors: 8,
      isNew: false
    },
    {
      id: 4,
      name: "Nike Air Force 1 '07",
      category: "Men's Shoes",
      price: "$90",
      image: "https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1887&q=80",
      colors: 12,
      isNew: false
    },
    {
      id: 5,
      name: "Nike React Infinity Run",
      category: "Running Shoes",
      price: "$160",
      image: "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2064&q=80",
      colors: 4,
      isNew: true
    },
    {
      id: 6,
      name: "Nike Blazer Mid '77",
      category: "Women's Shoes",
      price: "$100",
      image: "https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2012&q=80",
      colors: 6,
      isNew: false
    }
  ]

  return (
    <section className="featured-products section-padding">
      <div className="container">
        <div className="section-header">
          <h2>Featured Products</h2>
          <p>Discover our latest and most popular sneakers</p>
        </div>

        <div className="products-grid">
          {products.map(product => (
            <div key={product.id} className="product-card">
              <div className="product-image-container">
                <img 
                  src={product.image} 
                  alt={product.name}
                  className="product-image"
                />
                <button className="wishlist-btn">
                  <Heart size={20} />
                </button>
                {product.isNew && (
                  <span className="new-badge">New</span>
                )}
              </div>
              
              <div className="product-info">
                <h3 className="product-name">{product.name}</h3>
                <p className="product-category">{product.category}</p>
                <div className="product-details">
                  <span className="product-price">{product.price}</span>
                  <span className="product-colors">{product.colors} Colors</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="section-footer">
          <button className="btn-secondary">View All Products</button>
        </div>
      </div>
    </section>
  )
}

export default FeaturedProducts
